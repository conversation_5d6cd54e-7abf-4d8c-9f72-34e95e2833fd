# New state

<EpicVideo url="https://www.epicreact.dev/workshops/advanced-react-apis/new-state" />

👨‍💼 Our users want a counter component and it's working fine, but <PERSON><PERSON> has
said we can improve the implementation using `useReducer` (actually,
`useReducer` is absolutely overkill for a counter component like ours, but we'll
be using it to learn how to use it).

The emoji will guide you through this!

📜 Here are some handy references:

- [`useReducer` docs](https://react.dev/reference/react/useReducer)
- [How to implement useState with useReducer](https://kentcdodds.com/blog/how-to-implement-usestate-with-usereducer)
- [Should I useState or useReducer?](https://kentcdodds.com/blog/should-i-usestate-or-usereducer)
