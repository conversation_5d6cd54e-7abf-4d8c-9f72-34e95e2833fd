# State Optimization

<EpicVideo url="https://www.epicreact.dev/workshops/advanced-react-apis/dad-joke-break-state-optimization" />

👨‍💼 Great! Now you know how to prevent unnecessary state changes that trigger
rerenders. Good work.

🦉 Normally you don't want to add complexity to your code to improve performance
unless you have measured before/after to make certain that the change is actually
worth it. In this case, the performance improvement is likely to be negligible
unless you have a very large number of state updates happening in a short period
of time. But the added complexity is probably about as negligible as the
performance improvement, so either way it's a wash.
