# Action Function

<EpicVideo url="https://www.epicreact.dev/workshops/advanced-react-apis/action-function" />

👨‍💼 `this.setState` from class components can also accept a function. So let's
add support for that with our simulated `setState` function. See if you can
figure out how to make your reducer support both the object as in the last step
as well as a function callback:

```tsx
const [state, setState] = useReducer(countReducer, {
	count: initialCount,
})
const { count } = state
const increment = () =>
	setState((currentState) => ({ count: currentState.count + step }))
const decrement = () =>
	setState((currentState) => ({ count: currentState.count - step }))
```
