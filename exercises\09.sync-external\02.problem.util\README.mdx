# Make Store Utility

<EpicVideo url="https://www.epicreact.dev/workshops/advanced-react-apis/make-store-utility" />

👨‍💼 We want to make this utility generally useful so we can use it for any media
query. So please stick most of our logic in a `makeMediaQueryStore` function
and have that return a custom hook people can use to keep track of the current
media query's matching state.

It'll be something like this:

```tsx
export function makeMediaQueryStore(mediaQuery: string) {
	// ...
}

const useNarrowMediaQuery = makeMediaQueryStore('(max-width: 600px)')

function App() {
	const isNarrow = useNarrowMediaQuery()
	// ...
}
```
