# Handling Server Rendering

<EpicVideo url="https://www.epicreact.dev/workshops/advanced-react-apis/handling-server-rendering/solution" />

👨‍💼 Great work! You now know how to properly handle server rendering of something
we don't know until the client-render when it comes to an external store like
this.

🦉 There are more things you can do for different cases (like the user's
light/dark mode preference) to offer a better user experience. Check out
[`@epic-web/client-hints`](https://www.npmjs.com/package/@epic-web/client-hints)
to see how you can handle this even better if you're interested.
