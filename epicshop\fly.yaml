# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app: 'epicweb-dev-advanced-react-apis'
primary_region: sjc
kill_signal: SIGINT
kill_timeout: 5s
swap_size_mb: 512

experimental:
  auto_rollback: true

  attached:
    secrets: {}

services:
  - processes:
      - app
    protocol: tcp
    internal_port: 8080

    ports:
      - port: 80

        handlers:
          - http
        force_https: true
      - port: 443

        handlers:
          - tls
          - http

    concurrency:
      type: connections
      hard_limit: 100
      soft_limit: 80

    tcp_checks:
      - interval: 15s
        timeout: 2s
        grace_period: 1s

    http_checks:
      - interval: 10s
        timeout: 2s
        grace_period: 5s
        method: get
        path: /resources/healthcheck
        protocol: http
        tls_skip_verify: false
