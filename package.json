{"name": "advanced-react-apis", "private": true, "epicshop": {"title": "Advanced React APIs 🔥", "subtitle": "Learn the more advanced React APIs and different use cases to enable great user experiences.", "githubRepo": "https://github.com/epicweb-dev/advanced-react-apis", "stackBlitzConfig": {"view": "editor"}, "product": {"host": "www.epicreact.dev", "slug": "advanced-react-apis", "displayName": "EpicReact.dev", "displayNameShort": "Epic React", "logo": "/logo.svg", "discordChannelId": "1285244676286189569", "discordTags": ["1285246046498328627", "1285245763428810815"]}, "onboardingVideo": "https://www.epicweb.dev/tips/get-started-with-the-epic-workshop-app-for-react", "instructor": {"name": "Kent <PERSON>", "avatar": "/images/instructor.png", "𝕏": "kentcdodds"}}, "type": "module", "imports": {"#*": "./*"}, "prettier": "@epic-web/config/prettier", "scripts": {"postinstall": "cd ./epicshop && npm install", "start": "npx --prefix ./epicshop epicshop start", "dev": "npx --prefix ./epicshop epicshop start", "setup": "node ./epicshop/setup.js", "setup:custom": "node ./epicshop/setup-custom.js", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc -b"}, "keywords": [], "author": "<PERSON> <PERSON> <<EMAIL>> (https://kentcdodds.com/)", "license": "GPL-3.0-only", "dependencies": {"react": "19.0.0", "react-dom": "19.0.0"}, "devDependencies": {"@epic-web/config": "^1.16.3", "@epic-web/workshop-utils": "^6.20.2", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "eslint": "^9.16.0", "npm-run-all": "^4.1.5", "prettier": "^3.4.2", "typescript": "^5.7.2"}, "engines": {"node": ">=20", "npm": ">=9.3.0", "git": ">=2.18.0"}, "prettierIgnore": ["node_modules", "**/build/**", "**/public/build/**", ".env", "**/package.json", "**/tsconfig.json", "**/package-lock.json", "**/playwright-report/**"]}