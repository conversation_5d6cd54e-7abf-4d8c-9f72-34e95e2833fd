export type Message = { id: string; author: string; content: string }

export const allMessages: Array<Message> = [
	`<PERSON><PERSON>: Aren't you a little short to be a stormtrooper?`,
	`<PERSON>: What? Oh... the uniform. I'm <PERSON>. I'm here to rescue you.`,
	`<PERSON><PERSON>: You're who?`,
	`<PERSON>: I'm here to rescue you. I've got your R2 unit. I'm here with <PERSON>.`,
	`<PERSON><PERSON>: <PERSON> is here! Where is he?`,
	`<PERSON>: Come on!`,
	`<PERSON>: Will you forget it? I already tried it. It's magnetically sealed!`,
	`<PERSON><PERSON>: Put that thing away! You're going to get us all killed.`,
	`<PERSON>: Absolutely, Your Worship. Look, I had everything under control until you led us down here. You know, it's not going to take them long to figure out what happened to us.`,
	`<PERSON><PERSON>: It could be worse...`,
	`<PERSON>: It's worse.`,
	`Luke: There's something alive in here!`,
	`Han: That's your imagination.`,
	`Luke: Something just moves past my leg! Look! Did you see that?`,
	`<PERSON>: What?`,
	`Luke: Help!`,
	`<PERSON>: Luke! Luke! Luke!`,
	`<PERSON><PERSON>: Luke!`,
	`<PERSON><PERSON>: <PERSON>, <PERSON>, grab a hold of this.`,
	`Luke: Blast it, will you! My gun's jammed.`,
	`Han: Where?`,
	`Luke: Anywhere! Oh!!`,
	`<PERSON>: Luke! Luke!`,
	`<PERSON>ia: G<PERSON> him!`,
	`<PERSON><PERSON>: What happened?`,
	`Luke: I don't know, it just let go of me and disappeared...`,
	`<PERSON>: I've got a very bad feeling about this.`,
	`Luke: The walls are moving!`,
	`<PERSON><PERSON>: <PERSON>'t just stand there. Try to brace it with something.`,
	`Luke: Wait a minute!`,
	`Luke: Three<PERSON>! Come in Three<PERSON>! Three<PERSON>! Where could he be?`,
].map((m, i) => ({
	id: String(i),
	author: m.split(': ')[0]!,
	content: m.split(': ')[1]!,
}))
