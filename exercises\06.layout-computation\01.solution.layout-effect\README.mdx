# useLayoutEffect

<EpicVideo url="https://www.epicreact.dev/workshops/advanced-react-apis/uselayouteffect/solution" />

👨‍💼 Have you ever heard of the mechanic who charged $200 when all he did was
tighten a screw? The customer was outraged and asked for an itemized bill. The
mechanic sent him an invoice that read:

```
Tightening a screw: $1
Knowing which screw to tighten: $199
```

Even though this exercise was really easy, knowing when to use `useLayoutEffect`
is the hard part. It's like knowing which screw to tighten. Good job!
