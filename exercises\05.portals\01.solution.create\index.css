html,
body {
	margin: 0;
}

.app {
	margin: 40px auto;
	max-width: 1024px;
	form {
		text-align: center;
	}
}

.post-list {
	list-style: none;
	padding: 0;
	display: flex;
	gap: 20px;
	flex-wrap: wrap;
	justify-content: center;
	li {
		position: relative;
		border-radius: 0.5rem;
		overflow: hidden;
		border: 1px solid #ddd;
		width: 320px;
		transition: transform 0.2s ease-in-out;
		a {
			text-decoration: none;
			color: unset;
		}

		&:hover,
		&:has(*:focus),
		&:has(*:active) {
			transform: translate(0px, -6px);
		}

		.post-image {
			display: block;
			width: 100%;
			height: 200px;
		}

		button {
			position: absolute;
			font-size: 1.5rem;
			top: 20px;
			right: 20px;
			background: transparent;
			border: none;
			outline: none;
			&:hover,
			&:focus,
			&:active {
				animation: pulse 1.5s infinite;
			}
		}

		a {
			padding: 10px 10px;
			display: flex;
			gap: 8px;
			flex-direction: column;
			h2 {
				margin: 0;
				font-size: 1.5rem;
				font-weight: bold;
			}
			p {
				margin: 0;
				font-size: 1rem;
				color: #666;
			}
		}
	}
}

@keyframes pulse {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.3);
	}
	100% {
		transform: scale(1);
	}
}

.tooltip-container {
	position: absolute;
	pointer-events: none;
	left: 0;
	top: 0;
	transform: translate3d(var(--x), var(--y), 0);
	z-index: 10;
}

.tooltip {
	color: white;
	background: #222;
	border-radius: 4px;
	padding: 4px;
}
