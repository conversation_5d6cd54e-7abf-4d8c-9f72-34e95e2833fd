# useImperativeHandle

<EpicVideo url="https://www.epicreact.dev/workshops/advanced-react-apis/useimperativehandle" />

👨‍💼 We've got a new thing for you to work on.

🧝‍♂️ I've put together a `Scrollable` component which is a wrapper around a `div`
that has a way to scroll to the top and bottom of the content. We want to be
able to add buttons to the `App` that will allow users to scroll to the top and
bottom of the content when clicked.

👨‍💼 So we need you to `useImperativeHandle` to expose a `scrollToTop` and
`scrollToBottom` method from the `Scrollable` component. These methods are
already implemented, you just need to expose them.
