# Advanced React APIs 🔥

<EpicVideo url="https://www.epicreact.dev/workshops/advanced-react-apis/advanced-react-apis-intro" />

👨‍💼 Hello there! I'm <PERSON> the Product Manager and I'll be helping guide you
through all the things that our users want to see in our app that you'll be
working on in this workshop.

We're going to cover a lot of ground and a handful of components that need to be
enhanced for the features our users are looking for. You'll be building things
using React hooks like `useReducer`, `use`, `useLayoutEffect`,
`useSyncExternalStore`, and more. You'll even be building your own custom
hooks!

In addition to advanced hooks, we'll also be covering a couple advanced use
cases like focus management with `flushSync` as well as `createPortal`.

It's going to be a full experience, so let's get started!

<callout-info>
	🚨 Note because we're refactoring each step rather than changing behavior, the
	tests will all be working from the start. So the tests are just there to help
	you make sure you have not regressed any functionality in the course of your
	refactoring.
</callout-info>

🎵 Check out the workshop theme song! 🎶

<VideoEmbed
	title="Epic React: Advanced React APIs Theme Song"
	url="https://www.youtube.com/embed/uYTdH3rTjDQ?list=PLV5CVI1eNcJieZkL_q7M48PwEpeNT-sL1&rel=0"
/>
